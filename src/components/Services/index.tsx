"use client";
import { useEffect, useState } from "react";
import { Card, CardBody, Chip, Button } from "@heroui/react";
import {
  CheckCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";
import SectionTitle from "../Common/SectionTitle";
import PageHero from "../Common/PageHero";
import { StartProjectServicesCTA, GetQuoteServicesCTA } from "../CTAForms/ProjectCTAButtons";
import { servicesData } from "../../data/services";

const ServicesPage = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Service URL mapping
  const serviceUrls: Record<string, string> = {
    "custom-software": "/services/custom-software",
    "ai-ml": "/services/ai-machine-learning",
    "cybersecurity": "/services/cybersecurity-solutions",
    "cloud-devops": "/services/cloud-devops",
    "enterprise-consulting": "/services/enterprise-consulting",
    "talent-acquisition": "/services/talent-acquisition"
  };

  const services = servicesData.map(service => ({
    ...service,
    url: serviceUrls[service.id]
  }));

  // Debug: Log the number of services
  console.log('Total services loaded:', services.length);
  console.log('Service IDs:', services.map(s => s.id));
  console.log('Raw servicesData length:', servicesData.length);
  console.log('Raw servicesData IDs:', servicesData.map(s => s.id));

  return (
    <>
      <PageHero
        badge="Professional Services"
        title="Software Development Services"
        description="Transform your business with our comprehensive software development solutions, from custom applications to enterprise-grade systems."
        primaryButtonText="Start Your Project"
        primaryButtonLink="/contact"
        secondaryButtonText="Learn About Us"
        secondaryButtonLink="/about"
      />

      {/* Services Grid */}
      <section className="bg-white py-16 dark:bg-dark md:py-20 lg:py-28">
        <div className="container mx-auto">
          <div className="mb-16">
            <SectionTitle
              subtitle="Our Expertise"
              title="Comprehensive Software Solutions"
              paragraph="We deliver end-to-end software consulting services that drive innovation and accelerate your business growth."
              center
            />
          </div>

          <div className="grid gap-8 lg:grid-cols-2 xl:grid-cols-3">
            {services.map((service, index) => (
              <Card
                key={service.id}
                className="h-full hover:shadow-xl hover:-translate-y-2 transition-all duration-300"
              >
                <CardBody className="p-8">
                  <div className="mb-6">
                    <div className="flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-white shadow-lg">
                      <service.icon className="h-8 w-8" />
                    </div>
                  </div>
                  
                  <h3 className="mb-4 text-xl font-bold text-dark dark:text-white">
                    {service.title}
                  </h3>
                  
                  <p className="mb-6 text-body-color dark:text-dark-6">
                    {service.shortDescription}
                  </p>

                  <div className="mb-6">
                    <h4 className="mb-3 font-semibold text-dark dark:text-white">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-2 text-sm text-body-color dark:text-dark-6">
                          <CheckCircleIcon className="h-4 w-4 text-primary flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mb-6">
                    <h4 className="mb-3 font-semibold text-dark dark:text-white">Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.technologies.map((tech, idx) => (
                        <Chip key={idx} size="sm" variant="flat" color="primary">
                          {tech}
                        </Chip>
                      ))}
                    </div>
                  </div>

                  <div className="mt-auto pt-4">
                    <Link href={serviceUrls[service.id] || `/services/${service.id}`}>
                      <Button
                        color="primary"
                        className="w-full"
                        endContent={<ArrowRightIcon className="h-4 w-4" />}
                      >
                        Learn More & Get Quote
                      </Button>
                    </Link>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="bg-gray-50 py-16 dark:bg-dark-2 md:py-20 lg:py-28">
        <div className="container mx-auto">
          <div className="mb-16">
            <SectionTitle
              subtitle="Our Process"
              title="How We Deliver Excellence"
              paragraph="Our proven methodology ensures successful project delivery and long-term partnership success."
              center
            />
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                step: "01",
                title: "Discovery & Analysis",
                description: "We analyze your requirements, challenges, and goals to create a tailored solution strategy."
              },
              {
                step: "02",
                title: "Planning & Design",
                description: "Detailed project planning, architecture design, and technology selection for optimal results."
              },
              {
                step: "03",
                title: "Development & Testing",
                description: "Agile development with continuous testing, regular updates, and quality assurance."
              },
              {
                step: "04",
                title: "Deployment & Support",
                description: "Smooth deployment, team training, and ongoing support to ensure long-term success."
              }
            ].map((process, index) => (
              <Card key={process.step} className="text-center">
                <CardBody className="p-8">
                  <div className="mb-4">
                    <span className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-primary text-2xl font-bold text-white">
                      {process.step}
                    </span>
                  </div>
                  <h3 className="mb-3 text-lg font-semibold text-dark dark:text-white">
                    {process.title}
                  </h3>
                  <p className="text-body-color dark:text-dark-6">
                    {process.description}
                  </p>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="bg-primary py-16 md:py-20 lg:py-28">
        <div className="container mx-auto">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="mb-6 text-3xl font-bold text-white sm:text-4xl md:text-5xl">
              Ready to Transform Your Business?
            </h2>
            <p className="mb-8 text-lg text-white/90 sm:text-xl">
              Let's discuss how our software consulting services can accelerate your digital transformation and drive measurable results.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <StartProjectServicesCTA
                size="lg"
                className="bg-white text-primary hover:bg-blue-50 font-semibold"
                ctaText="Start Your Project"
                source="services-final-cta"
                analyticsEvent="services_start_project"
              />
              <GetQuoteServicesCTA
                variant="outline"
                size="lg"
                className="text-white border-white/30 hover:bg-white/10"
                ctaText="Get Free Estimate"
                source="services-final-cta"
                analyticsEvent="services_get_estimate"
              />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ServicesPage;
