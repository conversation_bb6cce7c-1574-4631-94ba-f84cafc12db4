"use client";

import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ody,
  <PERSON>ton,
  Chip,
  Accordion,
  AccordionItem,
} from '@heroui/react';
import {
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ArrowRightIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import PageHero from '../Common/PageHero';
import SectionTitle from '../Common/SectionTitle';

interface SimpleServiceDetailPageProps {
  service: {
    id: string;
    title: string;
    description: string;
    shortDescription: string;
    features: string[];
    technologies: string[];
    benefits: string[];
    process: string[];
    timeline: string;
    pricing: string;
    ctaText: string;
    ctaLink: string;
    caseStudy?: {
      title: string;
      description: string;
      results: string[];
    };
    // Extended properties for enhanced pages
    detailedServices?: Array<{
      title: string;
      description: string;
      features: string[];
    }>;
    pricingModels?: Array<{
      name: string;
      description: string;
      price: string;
      features: string[];
      recommended?: boolean;
    }>;
    faq?: Array<{
      question: string;
      answer: string;
    }>;
    testimonial?: {
      quote: string;
      author: string;
      title: string;
      company: string;
    };
  };
}

const SimpleServiceDetailPage: React.FC<SimpleServiceDetailPageProps> = ({ service }) => {
  return (
    <>
      <PageHero
        badge="Professional Service"
        title={service.title}
        description={service.shortDescription}
        primaryButtonText="Get Started"
        primaryButtonLink="/contact"
        secondaryButtonText="Schedule Consultation"
        secondaryButtonLink="/contact"
      />

      {/* Service Overview */}
      <section className="pb-12 pt-20 lg:pb-[90px] lg:pt-[120px]">
        <div className="container">
          <div className="mb-[60px]">
            <SectionTitle
              subtitle="Service Overview"
              title="Comprehensive Solution"
              paragraph={service.description}
              width="800px"
              center
            />
          </div>

          <div className="grid gap-8 lg:grid-cols-2">
            <Card className="h-full">
              <CardBody className="p-8">
                <h3 className="mb-6 text-2xl font-bold text-dark dark:text-white">
                  Key Features
                </h3>
                <ul className="space-y-4">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <CheckCircleIcon className="h-6 w-6 text-primary flex-shrink-0 mt-0.5" />
                      <span className="text-body-color dark:text-dark-6">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>

            <Card className="h-full">
              <CardBody className="p-8">
                <h3 className="mb-6 text-2xl font-bold text-dark dark:text-white">
                  Key Benefits
                </h3>
                <ul className="space-y-4">
                  {service.benefits.map((benefit, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <StarIcon className="h-6 w-6 text-primary flex-shrink-0 mt-0.5" />
                      <span className="text-body-color dark:text-dark-6">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardBody>
            </Card>
          </div>

          <div className="mt-8">
            <Card>
              <CardBody className="p-8">
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="text-center">
                    <ClockIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 className="text-lg font-semibold text-dark dark:text-white mb-2">Timeline</h4>
                    <p className="text-body-color dark:text-dark-6">{service.timeline}</p>
                  </div>
                  <div className="text-center">
                    <CurrencyDollarIcon className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 className="text-lg font-semibold text-dark dark:text-white mb-2">Pricing</h4>
                    <p className="text-body-color dark:text-dark-6">{service.pricing}</p>
                  </div>
                  <div className="text-center">
                    <div className="flex flex-wrap gap-2 justify-center">
                      {service.technologies.slice(0, 3).map((tech, idx) => (
                        <Chip key={idx} color="primary" variant="flat" size="sm">
                          {tech}
                        </Chip>
                      ))}
                      {service.technologies.length > 3 && (
                        <Chip color="primary" variant="flat" size="sm">
                          +{service.technologies.length - 3} more
                        </Chip>
                      )}
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>

      {/* Technologies */}
      <section className="bg-gray-1 py-20 dark:bg-dark-2">
        <div className="container">
          <div className="mb-[60px]">
            <SectionTitle
              subtitle="Technologies"
              title="Cutting-Edge Technology Stack"
              paragraph="We use the latest and most reliable technologies to build robust, scalable solutions."
              width="640px"
              center
            />
          </div>

          <div className="flex flex-wrap gap-4 justify-center">
            {service.technologies.map((tech, idx) => (
              <Chip key={idx} color="primary" variant="flat" className="text-lg px-6 py-3">
                {tech}
              </Chip>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <div className="container">
          <div className="mb-[60px]">
            <SectionTitle
              subtitle="Our Process"
              title="How We Deliver Excellence"
              paragraph="Our proven methodology ensures successful project delivery from start to finish."
              width="640px"
              center
            />
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {service.process.map((step, idx) => (
              <Card key={idx} className="text-center">
                <CardBody className="p-8">
                  <div className="mb-4">
                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white text-xl font-bold">
                      {String(idx + 1).padStart(2, '0')}
                    </div>
                  </div>
                  <h3 className="mb-3 text-lg font-semibold text-dark dark:text-white">
                    Step {idx + 1}
                  </h3>
                  <p className="text-body-color dark:text-dark-6">
                    {step}
                  </p>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Case Study */}
      {service.caseStudy && (
        <section className="bg-gray-1 py-20 dark:bg-dark-2">
          <div className="container">
            <div className="mb-[60px]">
              <SectionTitle
                subtitle="Success Story"
                title={service.caseStudy.title}
                paragraph={service.caseStudy.description}
                width="800px"
                center
              />
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {service.caseStudy.results.map((result, idx) => (
                <Card key={idx} className="text-center">
                  <CardBody className="p-6">
                    <div className="text-2xl font-bold text-primary mb-2">
                      {result.split(' ')[0]} {result.split(' ')[1]}
                    </div>
                    <p className="text-body-color dark:text-dark-6">
                      {result.split(' ').slice(2).join(' ')}
                    </p>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* FAQ Section */}
      {service.faq && (
        <section className="py-20">
          <div className="container">
            <div className="mb-[60px]">
              <SectionTitle
                subtitle="FAQ"
                title="Frequently Asked Questions"
                paragraph="Get answers to common questions about our services."
                width="640px"
                center
              />
            </div>

            <div className="max-w-4xl mx-auto">
              <Accordion variant="splitted">
                {service.faq.map((item, idx) => (
                  <AccordionItem key={idx} title={item.question}>
                    <p className="text-body-color dark:text-dark-6">{item.answer}</p>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20">
        <div className="container">
          <div className="rounded-2xl bg-gradient-to-r from-primary to-primary/80 px-8 py-16 text-center">
            <h2 className="mb-4 text-3xl font-bold text-white lg:text-4xl">
              Ready to Get Started?
            </h2>
            <p className="mb-8 text-lg text-white/90">
              Let's discuss your project and create a custom solution that drives results.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Link href={service.ctaLink}>
                <Button
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90"
                  endContent={<ArrowRightIcon className="h-5 w-5" />}
                >
                  {service.ctaText}
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="bordered"
                  className="border-white text-white hover:bg-white/10"
                >
                  Schedule Consultation
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default SimpleServiceDetailPage;
