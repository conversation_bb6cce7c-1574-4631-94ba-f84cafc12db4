"use client";

import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardBody,
  Chip,
  Divider,
  Progress
} from '@heroui/react';
import {
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import { StartProjectCTA, GetQuoteServicesCTA } from '../CTAForms/ProjectCTAButtons';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  service: {
    id: string;
    icon: React.ReactNode;
    title: string;
    description: string;
    features: string[];
    technologies: string[];
    benefits: string[];
    process: string[];
    timeline: string;
    pricing: string;
    caseStudy?: {
      title: string;
      description: string;
      results: string[];
    };
  };
}

const ServiceModal: React.FC<ServiceModalProps> = ({ isOpen, onClose, service }) => {
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[95vh]",
        body: "p-0"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1 p-6 pb-0">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-white">
              {service.icon}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-dark dark:text-white">
                {service.title}
              </h2>
              <p className="text-body-color dark:text-dark-6">
                Professional software development services
              </p>
            </div>
          </div>
        </ModalHeader>
        
        <ModalBody className="p-6">
          <div className="space-y-8">
            {/* Service Overview */}
            <Card>
              <CardBody className="p-6">
                <h3 className="text-xl font-semibold text-dark dark:text-white mb-4">
                  Service Overview
                </h3>
                <p className="text-body-color dark:text-dark-6 text-lg leading-relaxed">
                  {service.description}
                </p>
              </CardBody>
            </Card>

            {/* Key Features & Benefits */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-dark dark:text-white mb-4 flex items-center gap-2">
                    <CheckCircleIcon className="h-6 w-6 text-primary" />
                    Key Features
                  </h3>
                  <ul className="space-y-3">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <CheckCircleIcon className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                        <span className="text-body-color dark:text-dark-6">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardBody>
              </Card>

              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-dark dark:text-white mb-4 flex items-center gap-2">
                    <LightBulbIcon className="h-6 w-6 text-primary" />
                    Business Benefits
                  </h3>
                  <ul className="space-y-3">
                    {service.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <ChartBarIcon className="h-5 w-5 text-success flex-shrink-0 mt-0.5" />
                        <span className="text-body-color dark:text-dark-6">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardBody>
              </Card>
            </div>

            {/* Technologies */}
            <Card>
              <CardBody className="p-6">
                <h3 className="text-xl font-semibold text-dark dark:text-white mb-4">
                  Technologies & Tools
                </h3>
                <div className="flex flex-wrap gap-3">
                  {service.technologies.map((tech, idx) => (
                    <Chip 
                      key={idx} 
                      size="lg" 
                      variant="flat" 
                      color="primary"
                      className="px-4 py-2"
                    >
                      {tech}
                    </Chip>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Development Process */}
            <Card>
              <CardBody className="p-6">
                <h3 className="text-xl font-semibold text-dark dark:text-white mb-4 flex items-center gap-2">
                  <UserGroupIcon className="h-6 w-6 text-primary" />
                  Our Development Process
                </h3>
                <div className="space-y-4">
                  {service.process.map((step, idx) => (
                    <div key={idx} className="flex items-start gap-4">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white text-sm font-semibold flex-shrink-0">
                        {idx + 1}
                      </div>
                      <div className="flex-1">
                        <p className="text-body-color dark:text-dark-6">{step}</p>
                        {idx < service.process.length - 1 && (
                          <div className="mt-2">
                            <Progress 
                              value={(idx + 1) * (100 / service.process.length)} 
                              color="primary" 
                              size="sm"
                              className="max-w-md"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Timeline & Pricing */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-dark dark:text-white mb-4 flex items-center gap-2">
                    <ClockIcon className="h-6 w-6 text-primary" />
                    Timeline
                  </h3>
                  <p className="text-body-color dark:text-dark-6 text-lg">
                    {service.timeline}
                  </p>
                </CardBody>
              </Card>

              <Card>
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-dark dark:text-white mb-4 flex items-center gap-2">
                    <CurrencyDollarIcon className="h-6 w-6 text-primary" />
                    Investment
                  </h3>
                  <p className="text-body-color dark:text-dark-6 text-lg">
                    {service.pricing}
                  </p>
                </CardBody>
              </Card>
            </div>

            {/* Case Study (if available) */}
            {service.caseStudy && (
              <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-dark dark:text-white mb-4">
                    Success Story: {service.caseStudy.title}
                  </h3>
                  <p className="text-body-color dark:text-dark-6 mb-4">
                    {service.caseStudy.description}
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-dark dark:text-white">Results:</h4>
                    <ul className="space-y-2">
                      {service.caseStudy.results.map((result, idx) => (
                        <li key={idx} className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-success" />
                          <span className="text-body-color dark:text-dark-6">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        </ModalBody>
        
        <ModalFooter className="p-6 pt-0">
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <GetQuoteServicesCTA 
              variant="ghost"
              className="flex-1"
              source={`${service.id}-modal`}
            />
            <StartProjectCTA 
              className="flex-1"
              source={`${service.id}-modal`}
            />
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ServiceModal;
