"use client";
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Textarea, 
  Button, 
  Select, 
  SelectItem, 
  Chip,
  Checkbox,
  RadioGroup,
  Radio,
  Progress
} from '@heroui/react';
import { toast } from 'react-hot-toast';
import { 
  BuildingOfficeIcon, 
  CurrencyDollarIcon, 
  CalendarIcon,
  UserGroupIcon,
  CogIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface StartProjectFormProps {
  onSuccess?: (data: any) => void;
  onClose?: () => void;
  source?: string;
}

interface ProjectFormData {
  // Contact & Company Information
  name: string;
  email: string;
  phone: string;
  jobTitle: string;
  company: string;
  companySize: string;
  industry: string;
  
  // Project Details
  projectName: string;
  projectType: string;
  projectDescription: string;
  businessObjectives: string;
  
  // Technical Requirements
  platforms: string[];
  integrations: string[];
  userBase: string;
  performanceRequirements: string;
  securityRequirements: string;
  complianceRequirements: string[];
  
  // Current State
  existingSystems: string;
  currentTechStack: string[];
  currentChallenges: string;
  
  // Project Scope & Timeline
  projectScope: string;
  timeline: string;
  budgetRange: string;
  urgencyLevel: string;
  
  // Decision Making
  decisionTimeframe: string;
  decisionMakers: string;
  approvalProcess: string;
  
  // Team & Resources
  internalTeamSize: string;
  technicalExpertise: string;
  projectManager: boolean;
  
  // Next Steps
  preferredStartDate: string;
  criticalDeadlines: string;
  additionalRequirements: string;
  
  // Consent
  privacyConsent: boolean;
  marketingConsent: boolean;
}

const StartProjectForm: React.FC<StartProjectFormProps> = ({
  onSuccess,
  onClose,
  source = 'start-project-form'
}) => {
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    email: '',
    phone: '',
    jobTitle: '',
    company: '',
    companySize: '',
    industry: '',
    projectName: '',
    projectType: '',
    projectDescription: '',
    businessObjectives: '',
    platforms: [],
    integrations: [],
    userBase: '',
    performanceRequirements: '',
    securityRequirements: '',
    complianceRequirements: [],
    existingSystems: '',
    currentTechStack: [],
    currentChallenges: '',
    projectScope: '',
    timeline: '',
    budgetRange: '',
    urgencyLevel: 'high',
    decisionTimeframe: '',
    decisionMakers: '',
    approvalProcess: '',
    internalTeamSize: '',
    technicalExpertise: '',
    projectManager: false,
    preferredStartDate: '',
    criticalDeadlines: '',
    additionalRequirements: '',
    privacyConsent: false,
    marketingConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;

  // Form options
  const projectTypes = [
    { key: 'custom-software', label: 'Custom Software Development' },
    { key: 'web-application', label: 'Web Application' },
    { key: 'mobile-app', label: 'Mobile Application' },
    { key: 'enterprise-software', label: 'Enterprise Software Solution' },
    { key: 'ai-ml-solution', label: 'AI/ML Solution' },
    { key: 'cybersecurity-implementation', label: 'Cybersecurity Implementation' },
    { key: 'cloud-migration', label: 'Cloud Migration & Infrastructure' },
    { key: 'system-integration', label: 'System Integration' },
    { key: 'digital-transformation', label: 'Digital Transformation' }
  ];

  const companySizes = [
    { key: 'startup', label: 'Startup (1-10 employees)' },
    { key: 'small', label: 'Small Business (11-50 employees)' },
    { key: 'medium', label: 'Medium Business (51-200 employees)' },
    { key: 'large', label: 'Large Enterprise (201-1000 employees)' },
    { key: 'enterprise', label: 'Enterprise (1000+ employees)' }
  ];

  const industries = [
    { key: 'technology', label: 'Technology & Software' },
    { key: 'healthcare', label: 'Healthcare & Life Sciences' },
    { key: 'finance', label: 'Financial Services & Banking' },
    { key: 'retail', label: 'Retail & E-commerce' },
    { key: 'manufacturing', label: 'Manufacturing & Industrial' },
    { key: 'education', label: 'Education & Training' },
    { key: 'government', label: 'Government & Public Sector' },
    { key: 'real-estate', label: 'Real Estate & Construction' },
    { key: 'media', label: 'Media & Entertainment' },
    { key: 'logistics', label: 'Logistics & Transportation' },
    { key: 'other', label: 'Other' }
  ];

  const budgetRanges = [
    { key: '50k-100k', label: '$50K - $100K' },
    { key: '100k-250k', label: '$100K - $250K' },
    { key: '250k-500k', label: '$250K - $500K' },
    { key: '500k-1m', label: '$500K - $1M' },
    { key: '1m-2m', label: '$1M - $2M' },
    { key: '2m+', label: '$2M+' },
    { key: 'flexible', label: 'Flexible based on value' }
  ];

  const timelines = [
    { key: '3-months', label: '3 months or less' },
    { key: '3-6-months', label: '3-6 months' },
    { key: '6-12-months', label: '6-12 months' },
    { key: '12-18-months', label: '12-18 months' },
    { key: '18-months+', label: '18+ months' },
    { key: 'phased', label: 'Phased approach' }
  ];

  const platforms = [
    { key: 'web', label: 'Web Application' },
    { key: 'mobile-ios', label: 'iOS Mobile App' },
    { key: 'mobile-android', label: 'Android Mobile App' },
    { key: 'desktop', label: 'Desktop Application' },
    { key: 'api', label: 'API/Backend Services' },
    { key: 'cloud', label: 'Cloud Platform' },
    { key: 'embedded', label: 'Embedded Systems' }
  ];

  const complianceOptions = [
    { key: 'gdpr', label: 'GDPR' },
    { key: 'hipaa', label: 'HIPAA' },
    { key: 'sox', label: 'SOX' },
    { key: 'pci-dss', label: 'PCI DSS' },
    { key: 'iso-27001', label: 'ISO 27001' },
    { key: 'soc2', label: 'SOC 2' },
    { key: 'fips', label: 'FIPS' }
  ];

  const handleInputChange = (field: keyof ProjectFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.name && formData.email && formData.company && formData.jobTitle);
      case 2:
        return !!(formData.projectType && formData.projectDescription && formData.businessObjectives);
      case 3:
        return !!(formData.timeline && formData.budgetRange);
      case 4:
        return !!(formData.decisionTimeframe && formData.decisionMakers);
      case 5:
        return formData.privacyConsent;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(currentStep)) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        ...formData,
        leadSource: source,
        pageUrl: window.location.href,
        timestamp: new Date().toISOString(),
        formType: 'high_intent_project',
        utmParams: {
          source: new URLSearchParams(window.location.search).get('utm_source'),
          medium: new URLSearchParams(window.location.search).get('utm_medium'),
          campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
          term: new URLSearchParams(window.location.search).get('utm_term'),
          content: new URLSearchParams(window.location.search).get('utm_content')
        }
      };

      const response = await fetch('/api/n8n-webhooks/start-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Thank you! Our team will contact you within 2-4 hours.');
        onSuccess?.(result);
        
        // Track conversion
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'conversion', {
            event_category: 'High Intent Lead',
            event_label: 'start_project_complete',
            value: 1
          });
        }

        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          jobTitle: '',
          company: '',
          companySize: '',
          industry: '',
          projectName: '',
          projectType: '',
          projectDescription: '',
          businessObjectives: '',
          platforms: [],
          integrations: [],
          userBase: '',
          performanceRequirements: '',
          securityRequirements: '',
          complianceRequirements: [],
          existingSystems: '',
          currentTechStack: [],
          currentChallenges: '',
          projectScope: '',
          timeline: '',
          budgetRange: '',
          urgencyLevel: 'high',
          decisionTimeframe: '',
          decisionMakers: '',
          approvalProcess: '',
          internalTeamSize: '',
          technicalExpertise: '',
          projectManager: false,
          preferredStartDate: '',
          criticalDeadlines: '',
          additionalRequirements: '',
          privacyConsent: false,
          marketingConsent: false
        });
        setCurrentStep(1);
      } else {
        toast.error(result.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepTitle = (step: number): string => {
    switch (step) {
      case 1: return 'Contact & Company Information';
      case 2: return 'Project Overview';
      case 3: return 'Scope & Requirements';
      case 4: return 'Decision Process';
      case 5: return 'Final Details';
      default: return '';
    }
  };

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return <BuildingOfficeIcon className="h-5 w-5" />;
      case 2: return <DocumentTextIcon className="h-5 w-5" />;
      case 3: return <CogIcon className="h-5 w-5" />;
      case 4: return <UserGroupIcon className="h-5 w-5" />;
      case 5: return <CalendarIcon className="h-5 w-5" />;
      default: return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardBody className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-dark dark:text-white mb-2">
            Start Your Software Project
          </h2>
          <p className="text-body-color dark:text-dark-6">
            Tell us about your project requirements and we'll provide a comprehensive proposal and timeline.
          </p>
          
          {/* Progress indicator */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-dark dark:text-white">
                Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}
              </span>
              <span className="text-sm text-body-color dark:text-dark-6">
                {Math.round((currentStep / totalSteps) * 100)}% Complete
              </span>
            </div>
            <Progress 
              value={(currentStep / totalSteps) * 100} 
              color="primary"
              className="mb-4"
            />
            
            {/* Step indicators */}
            <div className="flex items-center justify-between">
              {Array.from({ length: totalSteps }, (_, i) => (
                <div key={i} className="flex flex-col items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    i + 1 <= currentStep 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}>
                    {i + 1 <= currentStep ? (
                      i + 1 === currentStep ? getStepIcon(i + 1) : '✓'
                    ) : (
                      i + 1
                    )}
                  </div>
                  <span className="text-xs text-center mt-1 text-body-color dark:text-dark-6 max-w-16">
                    {getStepTitle(i + 1).split(' ')[0]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Step 1: Contact & Company Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Full Name"
                  placeholder="Enter your full name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  isRequired
                  variant="flat"
                />

                <Input
                  label="Job Title"
                  placeholder="e.g., CTO, Project Manager"
                  value={formData.jobTitle}
                  onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                  isRequired
                  variant="flat"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Email Address"
                  placeholder="Enter your business email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  isRequired
                  variant="flat"
                />

                <Input
                  label="Phone Number"
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  variant="flat"
                />
              </div>

              <Input
                label="Company Name"
                placeholder="Enter your company name"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                isRequired
                variant="flat"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Company Size"
                  placeholder="Select company size"
                  selectedKeys={formData.companySize ? [formData.companySize] : []}
                  onSelectionChange={(keys) => handleInputChange('companySize', Array.from(keys)[0])}
                  variant="flat"
                >
                  {companySizes.map((size) => (
                    <SelectItem key={size.key} value={size.key}>
                      {size.label}
                    </SelectItem>
                  ))}
                </Select>

                <Select
                  label="Industry"
                  placeholder="Select your industry"
                  selectedKeys={formData.industry ? [formData.industry] : []}
                  onSelectionChange={(keys) => handleInputChange('industry', Array.from(keys)[0])}
                  variant="flat"
                >
                  {industries.map((industry) => (
                    <SelectItem key={industry.key} value={industry.key}>
                      {industry.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
          )}

          {/* Step 2: Project Overview */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <Input
                label="Project Name"
                placeholder="Give your project a name"
                value={formData.projectName}
                onChange={(e) => handleInputChange('projectName', e.target.value)}
                variant="flat"
              />

              <Select
                label="Project Type"
                placeholder="Select the type of project"
                selectedKeys={formData.projectType ? [formData.projectType] : []}
                onSelectionChange={(keys) => handleInputChange('projectType', Array.from(keys)[0])}
                isRequired
                variant="flat"
              >
                {projectTypes.map((type) => (
                  <SelectItem key={type.key} value={type.key}>
                    {type.label}
                  </SelectItem>
                ))}
              </Select>

              <Textarea
                label="Project Description"
                placeholder="Describe your project in detail. What are you trying to build?"
                value={formData.projectDescription}
                onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                isRequired
                variant="flat"
                minRows={4}
              />

              <Textarea
                label="Business Objectives"
                placeholder="What business problems will this project solve? What are your key goals?"
                value={formData.businessObjectives}
                onChange={(e) => handleInputChange('businessObjectives', e.target.value)}
                isRequired
                variant="flat"
                minRows={3}
              />

              <Textarea
                label="Current Challenges"
                placeholder="What challenges are you facing with your current systems or processes?"
                value={formData.currentChallenges}
                onChange={(e) => handleInputChange('currentChallenges', e.target.value)}
                variant="flat"
                minRows={3}
              />
            </div>
          )}

          {/* Step 3: Scope & Requirements */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Project Timeline"
                  placeholder="Expected project duration"
                  selectedKeys={formData.timeline ? [formData.timeline] : []}
                  onSelectionChange={(keys) => handleInputChange('timeline', Array.from(keys)[0])}
                  isRequired
                  variant="flat"
                >
                  {timelines.map((timeline) => (
                    <SelectItem key={timeline.key} value={timeline.key}>
                      {timeline.label}
                    </SelectItem>
                  ))}
                </Select>

                <Select
                  label="Budget Range"
                  placeholder="Select your budget range"
                  selectedKeys={formData.budgetRange ? [formData.budgetRange] : []}
                  onSelectionChange={(keys) => handleInputChange('budgetRange', Array.from(keys)[0])}
                  isRequired
                  variant="flat"
                >
                  {budgetRanges.map((budget) => (
                    <SelectItem key={budget.key} value={budget.key}>
                      {budget.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-dark dark:text-white mb-3">
                  Target Platforms (Select all that apply)
                </label>
                <div className="flex flex-wrap gap-2">
                  {platforms.map((platform) => (
                    <Chip
                      key={platform.key}
                      variant={formData.platforms.includes(platform.key) ? "solid" : "bordered"}
                      color={formData.platforms.includes(platform.key) ? "primary" : "default"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newPlatforms = formData.platforms.includes(platform.key)
                          ? formData.platforms.filter(p => p !== platform.key)
                          : [...formData.platforms, platform.key];
                        handleInputChange('platforms', newPlatforms);
                      }}
                    >
                      {platform.label}
                    </Chip>
                  ))}
                </div>
              </div>

              <Textarea
                label="Existing Systems"
                placeholder="Describe your current technology stack and systems that need integration"
                value={formData.existingSystems}
                onChange={(e) => handleInputChange('existingSystems', e.target.value)}
                variant="flat"
                minRows={3}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Textarea
                  label="Performance Requirements"
                  placeholder="Expected user load, response times, etc."
                  value={formData.performanceRequirements}
                  onChange={(e) => handleInputChange('performanceRequirements', e.target.value)}
                  variant="flat"
                  minRows={2}
                />

                <Textarea
                  label="Security Requirements"
                  placeholder="Data protection, authentication needs, etc."
                  value={formData.securityRequirements}
                  onChange={(e) => handleInputChange('securityRequirements', e.target.value)}
                  variant="flat"
                  minRows={2}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-dark dark:text-white mb-3">
                  Compliance Requirements (Select all that apply)
                </label>
                <div className="flex flex-wrap gap-2">
                  {complianceOptions.map((compliance) => (
                    <Chip
                      key={compliance.key}
                      variant={formData.complianceRequirements.includes(compliance.key) ? "solid" : "bordered"}
                      color={formData.complianceRequirements.includes(compliance.key) ? "primary" : "default"}
                      className="cursor-pointer"
                      onClick={() => {
                        const newCompliance = formData.complianceRequirements.includes(compliance.key)
                          ? formData.complianceRequirements.filter(c => c !== compliance.key)
                          : [...formData.complianceRequirements, compliance.key];
                        handleInputChange('complianceRequirements', newCompliance);
                      }}
                    >
                      {compliance.label}
                    </Chip>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Decision Process */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Decision Timeframe"
                  placeholder="When do you plan to make a decision?"
                  selectedKeys={formData.decisionTimeframe ? [formData.decisionTimeframe] : []}
                  onSelectionChange={(keys) => handleInputChange('decisionTimeframe', Array.from(keys)[0])}
                  isRequired
                  variant="flat"
                >
                  <SelectItem key="1-week" value="1-week">Within 1 week</SelectItem>
                  <SelectItem key="2-weeks" value="2-weeks">Within 2 weeks</SelectItem>
                  <SelectItem key="1-month" value="1-month">Within 1 month</SelectItem>
                  <SelectItem key="3-months" value="3-months">Within 3 months</SelectItem>
                  <SelectItem key="6-months" value="6-months">Within 6 months</SelectItem>
                </Select>

                <Input
                  label="Preferred Start Date"
                  placeholder="When would you like to start?"
                  type="date"
                  value={formData.preferredStartDate}
                  onChange={(e) => handleInputChange('preferredStartDate', e.target.value)}
                  variant="flat"
                />
              </div>

              <Textarea
                label="Decision Makers"
                placeholder="Who are the key decision makers involved in this project?"
                value={formData.decisionMakers}
                onChange={(e) => handleInputChange('decisionMakers', e.target.value)}
                isRequired
                variant="flat"
                minRows={2}
              />

              <Textarea
                label="Approval Process"
                placeholder="Describe your internal approval process for this project"
                value={formData.approvalProcess}
                onChange={(e) => handleInputChange('approvalProcess', e.target.value)}
                variant="flat"
                minRows={2}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Internal Team Size"
                  placeholder="Size of your internal team"
                  selectedKeys={formData.internalTeamSize ? [formData.internalTeamSize] : []}
                  onSelectionChange={(keys) => handleInputChange('internalTeamSize', Array.from(keys)[0])}
                  variant="flat"
                >
                  <SelectItem key="none" value="none">No internal team</SelectItem>
                  <SelectItem key="1-2" value="1-2">1-2 people</SelectItem>
                  <SelectItem key="3-5" value="3-5">3-5 people</SelectItem>
                  <SelectItem key="6-10" value="6-10">6-10 people</SelectItem>
                  <SelectItem key="10+" value="10+">10+ people</SelectItem>
                </Select>

                <Select
                  label="Technical Expertise Level"
                  placeholder="Your team's technical expertise"
                  selectedKeys={formData.technicalExpertise ? [formData.technicalExpertise] : []}
                  onSelectionChange={(keys) => handleInputChange('technicalExpertise', Array.from(keys)[0])}
                  variant="flat"
                >
                  <SelectItem key="none" value="none">No technical expertise</SelectItem>
                  <SelectItem key="basic" value="basic">Basic technical knowledge</SelectItem>
                  <SelectItem key="intermediate" value="intermediate">Intermediate technical skills</SelectItem>
                  <SelectItem key="advanced" value="advanced">Advanced technical team</SelectItem>
                  <SelectItem key="expert" value="expert">Expert technical team</SelectItem>
                </Select>
              </div>

              <Checkbox
                isSelected={formData.projectManager}
                onValueChange={(checked) => handleInputChange('projectManager', checked)}
              >
                We have a dedicated project manager for this initiative
              </Checkbox>

              <RadioGroup
                label="Project Urgency Level"
                value={formData.urgencyLevel}
                onValueChange={(value) => handleInputChange('urgencyLevel', value)}
                orientation="horizontal"
              >
                <Radio value="low">Low Priority</Radio>
                <Radio value="medium">Medium Priority</Radio>
                <Radio value="high">High Priority</Radio>
                <Radio value="urgent">Business Critical</Radio>
              </RadioGroup>
            </div>
          )}

          {/* Step 5: Final Details */}
          {currentStep === 5 && (
            <div className="space-y-6">
              <Textarea
                label="Critical Deadlines"
                placeholder="Are there any critical deadlines or milestones we should be aware of?"
                value={formData.criticalDeadlines}
                onChange={(e) => handleInputChange('criticalDeadlines', e.target.value)}
                variant="flat"
                minRows={2}
              />

              <Textarea
                label="Additional Requirements"
                placeholder="Any other requirements, constraints, or information you'd like to share?"
                value={formData.additionalRequirements}
                onChange={(e) => handleInputChange('additionalRequirements', e.target.value)}
                variant="flat"
                minRows={3}
              />

              <div className="space-y-3">
                <Checkbox
                  isSelected={formData.privacyConsent}
                  onValueChange={(checked) => handleInputChange('privacyConsent', checked)}
                  isRequired
                >
                  <span className="text-sm">
                    I agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and
                    <a href="/terms" className="text-primary hover:underline ml-1">Terms of Service</a>
                  </span>
                </Checkbox>

                <Checkbox
                  isSelected={formData.marketingConsent}
                  onValueChange={(checked) => handleInputChange('marketingConsent', checked)}
                >
                  <span className="text-sm">
                    I would like to receive updates about Digital Wave Systems services and industry insights
                  </span>
                </Checkbox>
              </div>

              <Card className="border-primary/20 bg-primary/5">
                <CardBody className="p-4">
                  <div className="flex items-start gap-3">
                    <CurrencyDollarIcon className="h-6 w-6 text-primary flex-shrink-0 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-dark dark:text-white mb-1">
                        What happens next?
                      </h4>
                      <ul className="text-sm text-body-color dark:text-dark-6 space-y-1">
                        <li>• Our senior team will review your requirements within 2-4 hours</li>
                        <li>• We'll schedule a detailed discovery call to discuss your project</li>
                        <li>• You'll receive a comprehensive proposal with timeline and pricing</li>
                        <li>• We can start your project as soon as you're ready</li>
                      </ul>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          )}

          <div className="flex justify-between mt-8">
            {currentStep > 1 && (
              <Button
                variant="bordered"
                onPress={handlePrevious}
                isDisabled={isSubmitting}
              >
                Previous
              </Button>
            )}
            
            <div className="ml-auto">
              {currentStep < totalSteps ? (
                <Button
                  color="primary"
                  onPress={handleNext}
                  isDisabled={!validateStep(currentStep)}
                >
                  Next Step
                </Button>
              ) : (
                <Button
                  color="primary"
                  type="submit"
                  isLoading={isSubmitting}
                  isDisabled={!validateStep(currentStep)}
                  size="lg"
                >
                  Submit Project Request
                </Button>
              )}
            </div>
          </div>
        </form>

        {onClose && (
          <Button
            variant="light"
            onPress={onClose}
            className="absolute top-4 right-4"
          >
            ×
          </Button>
        )}
      </CardBody>
    </Card>
  );
};

export default StartProjectForm;
