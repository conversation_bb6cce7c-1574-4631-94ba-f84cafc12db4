"use client";
import React, { useState } from 'react';
import { Button, Input, Textarea, Card, CardBody, Divider, Chip } from '@heroui/react';
import { 
  EnvelopeIcon,
  NewspaperIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface BlogSubscriptionFormProps {
  onClose: () => void;
  source?: string;
  analyticsEvent?: string;
}

const BlogSubscriptionForm: React.FC<BlogSubscriptionFormProps> = ({ 
  onClose, 
  source = 'blog-subscription',
  analyticsEvent = 'blog_subscription_submit'
}) => {
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    interests: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrorMessage('');

    try {
      // Validate required fields
      if (!formData.email || !formData.firstName) {
        throw new Error('Please fill in all required fields');
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Prepare data for n8n webhook
      const subscriptionData = {
        type: 'blog-subscription',
        email: formData.email,
        firstName: formData.firstName,
        interests: formData.interests,
        source: source,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        analyticsEvent: analyticsEvent
      };

      // Send to n8n webhook
      const response = await fetch('/api/n8n-webhooks/blog-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscriptionData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to subscribe. Please try again.');
      }

      // Track analytics event
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', analyticsEvent, {
          event_category: 'Blog Subscription',
          event_label: source,
          value: 1
        });
      }

      setSubmitStatus('success');
      
      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 2000);

    } catch (error) {
      console.error('Blog subscription error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'An unexpected error occurred');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitStatus === 'success') {
    return (
      <div className="text-center py-6 sm:py-8 px-2">
        <div className="mb-4 sm:mb-6">
          <CheckCircleIcon className="h-12 w-12 sm:h-16 sm:w-16 text-success mx-auto mb-3 sm:mb-4" />
          <h3 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
            Welcome to Our Community! 🎉
          </h3>
          <p className="text-sm sm:text-base text-default-600 px-2">
            Thank you for subscribing to our blog! You'll receive the latest insights, tutorials, and industry updates directly in your inbox.
          </p>
        </div>
        <div className="bg-success/10 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4 mx-2">
          <p className="text-success font-medium text-sm sm:text-base break-all">
            ✓ Subscription confirmed for {formData.email}
          </p>
        </div>
        <p className="text-xs sm:text-sm text-default-500">
          This window will close automatically...
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex justify-center mb-4">
          <div className="bg-primary/10 p-3 rounded-full">
            <NewspaperIcon className="h-8 w-8 text-primary" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Subscribe to Our Blog
        </h2>
        <p className="text-default-600">
          Get the latest software development insights, tutorials, and industry trends delivered to your inbox.
        </p>
      </div>

      {/* Benefits */}
      <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
        <CardBody className="p-4">
          <h3 className="font-semibold text-foreground mb-3 flex items-center gap-2">
            <EnvelopeIcon className="h-5 w-5 text-primary" />
            What you'll receive:
          </h3>
          <ul className="space-y-2 text-sm text-default-600">
            <li className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-success flex-shrink-0" />
              Weekly development insights and best practices
            </li>
            <li className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-success flex-shrink-0" />
              Exclusive tutorials and code examples
            </li>
            <li className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-success flex-shrink-0" />
              Industry trends and technology updates
            </li>
            <li className="flex items-center gap-2">
              <CheckCircleIcon className="h-4 w-4 text-success flex-shrink-0" />
              Early access to new content and resources
            </li>
          </ul>
        </CardBody>
      </Card>

      {/* Form Fields */}
      <div className="space-y-4">
        <Input
          type="email"
          label="Email Address"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          startContent={<EnvelopeIcon className="h-4 w-4 text-default-400" />}
          isRequired
          variant="bordered"
          classNames={{
            input: "text-foreground",
            label: "text-foreground"
          }}
        />

        <Input
          type="text"
          label="First Name"
          placeholder="Your first name"
          value={formData.firstName}
          onChange={(e) => handleInputChange('firstName', e.target.value)}
          isRequired
          variant="bordered"
          classNames={{
            input: "text-foreground",
            label: "text-foreground"
          }}
        />

        <Textarea
          label="Areas of Interest (Optional)"
          placeholder="e.g., React, Python, DevOps, AI/ML, Mobile Development..."
          value={formData.interests}
          onChange={(e) => handleInputChange('interests', e.target.value)}
          variant="bordered"
          minRows={2}
          maxRows={4}
          classNames={{
            input: "text-foreground",
            label: "text-foreground"
          }}
        />
      </div>

      {/* Error Message */}
      {submitStatus === 'error' && (
        <div className="bg-danger/10 border border-danger/20 rounded-lg p-4">
          <div className="flex items-center gap-2 text-danger">
            <ExclamationTriangleIcon className="h-5 w-5 flex-shrink-0" />
            <span className="font-medium">Subscription Failed</span>
          </div>
          <p className="text-danger/80 text-sm mt-1">{errorMessage}</p>
        </div>
      )}

      <Divider />

      {/* Submit Button */}
      <div className="flex flex-col gap-3">
        <Button
          type="submit"
          color="primary"
          size="lg"
          className="w-full font-semibold"
          isLoading={isSubmitting}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Subscribing...' : 'Subscribe to Blog Updates'}
        </Button>
        
        <div className="text-center">
          <p className="text-xs text-default-500">
            By subscribing, you agree to receive email updates. You can unsubscribe at any time.
          </p>
        </div>
      </div>

      {/* Trust Indicators */}
      <div className="flex justify-center gap-2 pt-2">
        <Chip size="sm" variant="flat" color="success">
          No Spam
        </Chip>
        <Chip size="sm" variant="flat" color="primary">
          Weekly Updates
        </Chip>
        <Chip size="sm" variant="flat" color="secondary">
          Easy Unsubscribe
        </Chip>
      </div>
    </form>
  );
};

export default BlogSubscriptionForm;
