"use client";
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Button, 
  Select, 
  SelectItem, 
  RadioGroup,
  Radio,
  Checkbox
} from '@heroui/react';
import { toast } from 'react-hot-toast';
import { 
  PhoneIcon, 
  VideoCameraIcon, 
  ClockIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface GetQuoteFormProps {
  onSuccess?: (data: any) => void;
  onClose?: () => void;
  source?: string;
}

interface QuoteFormData {
  // Basic Contact Information
  name: string;
  email: string;
  phone: string;
  company: string;
  
  // Service Interest
  serviceInterest: string;
  
  // Consultation Preference
  consultationType: 'video' | 'phone' | 'email';
  
  // Timeline Flexibility
  timelineFlexibility: 'immediate' | 'flexible' | 'exploring';
  
  // Consent
  privacyConsent: boolean;
}

const GetQuoteForm: React.FC<GetQuoteFormProps> = ({
  onSuccess,
  onClose,
  source = 'get-quote-form'
}) => {
  const [formData, setFormData] = useState<QuoteFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    serviceInterest: '',
    consultationType: 'video',
    timelineFlexibility: 'flexible',
    privacyConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConsultationScheduling, setShowConsultationScheduling] = useState(false);

  const serviceOptions = [
    { key: 'custom-software', label: 'Custom Software Development' },
    { key: 'web-application', label: 'Web Application' },
    { key: 'mobile-app', label: 'Mobile App Development' },
    { key: 'ai-ml', label: 'AI & Machine Learning' },
    { key: 'cybersecurity', label: 'Cybersecurity Solutions' },
    { key: 'cloud-devops', label: 'Cloud & DevOps' },
    { key: 'enterprise-solutions', label: 'Enterprise Solutions' },
    { key: 'talent-acquisition', label: 'Talent Acquisition' },
    { key: 'not-sure', label: 'Not Sure - Need Guidance' }
  ];

  const handleInputChange = (field: keyof QuoteFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    return !!(
      formData.name.trim() &&
      formData.email.trim() &&
      formData.serviceInterest &&
      formData.privacyConsent
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fill in all required fields and accept the privacy policy');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        ...formData,
        leadSource: source,
        pageUrl: window.location.href,
        timestamp: new Date().toISOString(),
        formType: 'lead_generation',
        utmParams: {
          source: new URLSearchParams(window.location.search).get('utm_source'),
          medium: new URLSearchParams(window.location.search).get('utm_medium'),
          campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
          term: new URLSearchParams(window.location.search).get('utm_term'),
          content: new URLSearchParams(window.location.search).get('utm_content')
        }
      };

      const response = await fetch('/api/n8n-webhooks/get-quote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Thank you! We\'ll be in touch soon with helpful resources.');
        
        // Track conversion
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'conversion', {
            event_category: 'Lead Generation',
            event_label: 'get_quote_complete',
            value: 1
          });
        }

        onSuccess?.(result);
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          serviceInterest: '',
          consultationType: 'video',
          timelineFlexibility: 'flexible',
          privacyConsent: false
        });
      } else {
        toast.error(result.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleScheduleConsultation = () => {
    if (formData.consultationType === 'video') {
      setShowConsultationScheduling(true);
    } else if (formData.consultationType === 'phone') {
      // Handle phone consultation request
      handlePhoneConsultation();
    } else {
      // Email consultation - just submit the form
      const syntheticEvent = {
        preventDefault: () => {},
        target: {},
        currentTarget: {}
      } as React.FormEvent;
      handleSubmit(syntheticEvent);
    }
  };

  const handlePhoneConsultation = async () => {
    if (!validateForm()) {
      toast.error('Please fill in all required fields first');
      return;
    }

    setIsSubmitting(true);

    try {
      // Map timelineFlexibility to urgency for quick-quote endpoint
      const urgencyMapping = {
        'immediate': 'asap',
        'flexible': 'this-week',
        'exploring': 'exploring'
      };

      const payload = {
        ...formData,
        consultationType: 'phone',
        urgency: urgencyMapping[formData.timelineFlexibility] || 'this-week',
        leadSource: source,
        pageUrl: window.location.href,
        timestamp: new Date().toISOString(),
        intent: 'request_phone_consultation'
      };

      const response = await fetch('/api/n8n-webhooks/quick-quote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Perfect! We\'ll call you within 2 hours.');
        onSuccess?.(result);
      } else {
        toast.error(result.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Phone consultation error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calendly Widget Component
  const CalendlyWidget = () => (
    <div className="mt-6">
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
            Schedule Your Video Consultation
          </h3>
          <div className="bg-white dark:bg-dark rounded-lg" style={{ height: '600px' }}>
            <iframe
              src={`https://calendly.com/your-calendly-link?hide_event_type_details=1&hide_gdpr_banner=1&prefill_name=${encodeURIComponent(formData.name)}&prefill_email=${encodeURIComponent(formData.email)}`}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              title="Schedule Consultation"
            />
          </div>
          <div className="flex justify-between items-center mt-4">
            <p className="text-sm text-body-color dark:text-dark-6">
              Can't find a suitable time?
            </p>
            <div className="flex gap-2">
              <Button 
                variant="light"
                size="sm"
                onPress={() => {
                  setShowConsultationScheduling(false);
                  handleInputChange('consultationType', 'phone');
                }}
              >
                Request Phone Call
              </Button>
              <Button 
                variant="light"
                size="sm"
                onPress={() => setShowConsultationScheduling(false)}
              >
                Back to Form
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  if (showConsultationScheduling && formData.consultationType === 'video') {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardBody className="p-6">
          <CalendlyWidget />
          {onClose && (
            <Button
              variant="light"
              onPress={onClose}
              className="absolute top-4 right-4"
            >
              ×
            </Button>
          )}
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg">
      <CardBody className="p-4 sm:p-6 lg:p-8">
        <div className="mb-4 sm:mb-6">
          <h2 className="text-xl sm:text-2xl font-bold text-dark dark:text-white mb-2">
            Get Your Development Quote
          </h2>
          <p className="text-sm sm:text-base text-body-color dark:text-dark-6 leading-relaxed">
            Tell us about your project needs and we'll provide you with helpful resources and next steps.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Contact Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Full Name"
                placeholder="Enter your name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                isRequired
                variant="bordered"
                size="md"
                classNames={{
                  input: "text-sm",
                  label: "text-xs font-medium"
                }}
              />

              <Input
                label="Email Address"
                placeholder="Enter your email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                isRequired
                variant="bordered"
                size="md"
                classNames={{
                  input: "text-sm",
                  label: "text-xs font-medium"
                }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Phone Number"
                placeholder="Enter your phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                variant="bordered"
                size="md"
                description="Optional - for phone consultations"
                classNames={{
                  input: "text-sm",
                  label: "text-xs font-medium",
                  description: "text-xs"
                }}
              />

              <Input
                label="Company Name"
                placeholder="Enter company name"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                variant="bordered"
                size="md"
                classNames={{
                  input: "text-sm",
                  label: "text-xs font-medium"
                }}
              />
            </div>
          </div>

          {/* Service Interest */}
          <div className="space-y-2">
            <Select
              label="What type of development are you interested in?"
              placeholder="Select a service"
              selectedKeys={formData.serviceInterest ? [formData.serviceInterest] : []}
              onSelectionChange={(keys) => handleInputChange('serviceInterest', Array.from(keys)[0])}
              isRequired
              variant="bordered"
              size="md"
              classNames={{
                trigger: "min-h-[44px]",
                label: "text-xs font-medium",
                value: "text-sm"
              }}
            >
              {serviceOptions.map((service) => (
                <SelectItem key={service.key}>
                  {service.label}
                </SelectItem>
              ))}
            </Select>
          </div>

          {/* Timeline Flexibility */}
          <div className="space-y-3">
            <RadioGroup
              label="What's your timeline?"
              value={formData.timelineFlexibility}
              onValueChange={(value) => handleInputChange('timelineFlexibility', value)}
              orientation="vertical"
              className="gap-2"
              classNames={{
                label: "text-sm font-medium text-dark dark:text-white mb-2",
                wrapper: "gap-2"
              }}
            >
              <Radio
                value="immediate"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-red-500" />
                  <div>
                    <div className="text-sm font-medium">Immediate</div>
                    <div className="text-xs text-body-color">Need to start ASAP</div>
                  </div>
                </div>
              </Radio>
              <Radio
                value="flexible"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-sm font-medium">Flexible</div>
                    <div className="text-xs text-body-color">Have some flexibility</div>
                  </div>
                </div>
              </Radio>
              <Radio
                value="exploring"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <DocumentTextIcon className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="text-sm font-medium">Exploring</div>
                    <div className="text-xs text-body-color">Just exploring options</div>
                  </div>
                </div>
              </Radio>
            </RadioGroup>
          </div>

          {/* Consultation Type Selection */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-dark dark:text-white">
              How would you like to connect?
            </h3>

            <RadioGroup
              value={formData.consultationType}
              onValueChange={(value) => handleInputChange('consultationType', value)}
              orientation="vertical"
              className="gap-2"
              classNames={{
                wrapper: "gap-2"
              }}
            >
              <Radio
                value="video"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <VideoCameraIcon className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-sm font-medium">Video Call</div>
                    <div className="text-xs text-body-color">Schedule a video call</div>
                  </div>
                </div>
              </Radio>
              <Radio
                value="phone"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <PhoneIcon className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-sm font-medium">Phone Call</div>
                    <div className="text-xs text-body-color">Get a phone call</div>
                  </div>
                </div>
              </Radio>
              <Radio
                value="email"
                size="sm"
                classNames={{
                  base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-md gap-3 p-3 border border-transparent data-[selected=true]:border-primary",
                  wrapper: "group-data-[selected=true]:border-primary",
                  labelWrapper: "flex-1"
                }}
              >
                <div className="flex items-center gap-2">
                  <DocumentTextIcon className="h-4 w-4 text-primary" />
                  <div>
                    <div className="text-sm font-medium">Email Only</div>
                    <div className="text-xs text-body-color">Email communication</div>
                  </div>
                </div>
              </Radio>
            </RadioGroup>
          </div>

          {/* Privacy Consent */}
          <div className="space-y-3 pt-3 border-t border-divider">
            <Checkbox
              isSelected={formData.privacyConsent}
              onValueChange={(checked) => handleInputChange('privacyConsent', checked)}
              isRequired
              size="md"
              classNames={{
                base: "inline-flex max-w-full w-full bg-content1 m-0",
                label: "w-full"
              }}
            >
              <span className="text-xs leading-relaxed">
                I agree to the <a href="/privacy" className="text-primary hover:underline font-medium">Privacy Policy</a> and
                consent to being contacted about development services.
              </span>
            </Checkbox>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 pt-4">
            {formData.consultationType === 'email' ? (
              <Button
                color="primary"
                type="submit"
                isLoading={isSubmitting}
                isDisabled={!validateForm()}
                size="md"
                className="w-full h-11 text-sm font-semibold"
                radius="md"
              >
                Get Quote & Resources
              </Button>
            ) : (
              <Button
                color="primary"
                onPress={handleScheduleConsultation}
                isLoading={isSubmitting}
                isDisabled={!validateForm()}
                size="md"
                className="w-full h-11 text-sm font-semibold"
                radius="md"
              >
                {formData.consultationType === 'video'
                  ? 'Schedule Video Call'
                  : 'Request Phone Call'
                }
              </Button>
            )}

            {formData.consultationType !== 'email' && (
              <Button
                variant="bordered"
                onPress={() => {
                  const syntheticEvent = {
                    preventDefault: () => {},
                    target: {},
                    currentTarget: {}
                  } as React.FormEvent;
                  handleSubmit(syntheticEvent);
                }}
                isDisabled={!validateForm() || isSubmitting}
                size="md"
                className="w-full h-10 text-sm"
                radius="md"
              >
                Just Send Me Information
              </Button>
            )}
          </div>
        </form>

        {onClose && (
          <Button
            variant="light"
            onPress={onClose}
            className="absolute top-4 right-4"
          >
            ×
          </Button>
        )}
      </CardBody>
    </Card>
  );
};

export default GetQuoteForm;
