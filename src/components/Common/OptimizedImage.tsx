"use client";

import Image, { ImageProps } from 'next/image';
import { useState, useRef, useEffect } from 'react';
import { createLazyLoadObserver, getOptimizedImageProps } from '@/utils/performance';

interface OptimizedImageProps extends Omit<ImageProps, 'loading' | 'placeholder'> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
  className?: string;
  lazy?: boolean;
  showPlaceholder?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  priority = false,
  className = '',
  lazy = true,
  showPlaceholder = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = createLazyLoadObserver(() => {
      setIsInView(true);
    });

    if (observer && imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (observer && imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, [lazy, priority, isInView]);

  const optimizedProps = getOptimizedImageProps(src, alt, width, height);

  return (
    <div 
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ 
        width: width || '100%', 
        height: height || 'auto',
        aspectRatio: width && height ? `${width}/${height}` : undefined
      }}
    >
      {isInView && (
        <Image
          {...optimizedProps}
          {...props}
          priority={priority}
          loading={priority ? 'eager' : 'lazy'}
          placeholder={showPlaceholder ? 'blur' : 'empty'}
          onLoad={() => setIsLoaded(true)}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          } ${props.className || ''}`}
        />
      )}
      
      {/* Loading placeholder */}
      {!isLoaded && showPlaceholder && (
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
