"use client";
import { useEffect, useState } from "react";
import { <PERSON>, Button } from "@heroui/react";
import Link from "next/link";
import { StartProjectCTA, GetQuoteServicesCTA } from "../CTAForms/ProjectCTAButtons";

interface PageHeroProps {
  badge?: string;
  title: string;
  description: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  primaryCTA?: React.ReactNode;
  secondaryCTA?: React.ReactNode;
  backgroundImage?: string;
}

const PageHero = ({
  badge,
  title,
  description,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink,
  primaryCTA,
  secondaryCTA,
  backgroundImage,
}: PageHeroProps) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Helper function to determine if button text suggests a project start action
  const isProjectStartAction = (text: string) => {
    const projectKeywords = ['start', 'begin', 'launch', 'project', 'consultation', 'schedule'];
    return projectKeywords.some(keyword => text.toLowerCase().includes(keyword));
  };

  // Helper function to determine if button text suggests a quote action
  const isQuoteAction = (text: string) => {
    const quoteKeywords = ['quote', 'estimate', 'pricing', 'cost', 'contact', 'get in touch'];
    return quoteKeywords.some(keyword => text.toLowerCase().includes(keyword));
  };

  // Helper function to determine if button text suggests a blog subscription action
  const isBlogSubscriptionAction = (text: string) => {
    const subscriptionKeywords = ['subscribe', 'newsletter', 'updates', 'blog', 'insights'];
    return subscriptionKeywords.some(keyword => text.toLowerCase().includes(keyword));
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-primary via-primary/90 to-primary/80 pb-16 pt-[120px] md:pb-20 md:pt-[150px] xl:pb-25 xl:pt-[180px]">
      <div className="container mx-auto">
        <div className="flex flex-wrap items-center">
          <div className="w-full px-4">
            <div className="mx-auto max-w-4xl text-center">
              {badge && (
                <Chip 
                  color="default" 
                  variant="flat" 
                  className="mb-6 bg-white/20 text-white backdrop-blur-sm"
                >
                  {badge}
                </Chip>
              )}
              <h1 className="mb-6 text-4xl font-bold leading-tight text-white sm:text-5xl md:text-6xl">
                {title}
              </h1>
              <p className="mb-8 text-lg text-white/90 sm:text-xl md:text-2xl">
                {description}
              </p>
              
              {(primaryCTA || secondaryCTA || primaryButtonText || secondaryButtonText) && (
                <div className="flex flex-col sm:flex-row justify-center gap-4">
                  {primaryCTA || (primaryButtonText && (
                    // Auto-convert contact links or project-related text to n8n modals
                    primaryButtonLink === "/contact" || isProjectStartAction(primaryButtonText) ? (
                      <StartProjectCTA
                        variant="primary"
                        size="lg"
                        className="bg-white text-primary hover:bg-blue-50 font-semibold"
                        source="page-hero"
                        analyticsEvent="page_hero_start_project"
                        ctaText={primaryButtonText}
                      />
                    ) : primaryButtonLink ? (
                      <Button
                        as={Link}
                        href={primaryButtonLink}
                        size="lg"
                        className="bg-white text-primary hover:bg-blue-50 font-semibold"
                      >
                        {primaryButtonText}
                      </Button>
                    ) : (
                      <StartProjectCTA
                        variant="primary"
                        size="lg"
                        className="bg-white text-primary hover:bg-blue-50 font-semibold"
                        source="page-hero"
                        analyticsEvent="page_hero_start_project"
                        ctaText={primaryButtonText}
                      />
                    )
                  ))}
                  {secondaryCTA || (secondaryButtonText && (
                    // Auto-convert contact links or quote-related text to n8n modals
                    secondaryButtonLink === "/contact" || isQuoteAction(secondaryButtonText) ? (
                      <GetQuoteServicesCTA
                        variant="outline"
                        size="lg"
                        className="text-white border-white/30 hover:bg-white/10 font-medium"
                        source="page-hero"
                        analyticsEvent="page_hero_get_quote"
                        ctaText={secondaryButtonText}
                      />
                    ) : secondaryButtonLink ? (
                      <Button
                        as={Link}
                        href={secondaryButtonLink}
                        variant="bordered"
                        size="lg"
                        className="text-white border-white/30 hover:bg-white/10 font-medium"
                      >
                        {secondaryButtonText}
                      </Button>
                    ) : (
                      <GetQuoteServicesCTA
                        variant="outline"
                        size="lg"
                        className="text-white border-white/30 hover:bg-white/10 font-medium"
                        source="page-hero"
                        analyticsEvent="page_hero_get_quote"
                        ctaText={secondaryButtonText}
                      />
                    )
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PageHero;
