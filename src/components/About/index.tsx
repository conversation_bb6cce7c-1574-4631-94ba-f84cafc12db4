import Image from "next/image";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Divider, Spacer } from "@heroui/react";
import { UserGroupIcon, PuzzlePieceIcon, ShieldCheckIcon, BoltIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { StartProjectCTA } from "../CTAForms/ProjectCTAButtons";

const About = () => {
  return (
    <section
      id="about"
      className="bg-gradient-to-b from-white to-gray-50 pb-16 pt-20 dark:bg-dark-2 dark:from-dark-2 dark:to-dark-3 lg:pb-[90px] lg:pt-[120px]"
    >
      <div className="container">
        <div className="wow fadeInUp" data-wow-delay=".2s">
          <div className="-mx-4 flex flex-wrap items-center">
            <div className="w-full px-4 lg:w-1/2">
              <div className="mb-12 max-w-[570px] lg:mb-0">
                <Chip
                  color="primary"
                  variant="flat"
                  className="mb-4"
                >
                  About Digital Wave Systems
                </Chip>
                <h2 className="mb-6 text-3xl font-bold leading-tight text-dark dark:text-white sm:text-[40px] sm:leading-[1.2]">
                  Your Trusted <span className="text-primary">Software Consulting</span> Partner
                </h2>
                <p className="mb-5 text-base leading-relaxed text-body-color dark:text-[#8890AD]">
                  Digital Wave Systems is a premier software consulting firm with over a decade of experience delivering transformative technology solutions. We partner with Fortune 500 companies and innovative startups to architect, develop, and deploy enterprise-grade software that drives measurable business outcomes.
                </p>
                <p className="mb-10 text-base leading-relaxed text-body-color dark:text-[#8890AD]">
                  Our multidisciplinary team of senior engineers, AI specialists, and cybersecurity experts brings deep technical expertise across modern technology stacks. From custom software development and AI integration to cybersecurity consulting and cloud architecture, we deliver solutions that scale with your business and exceed industry standards.
                </p>

                <div className="flex flex-wrap items-center gap-5">
                  <StartProjectCTA
                    variant="primary"
                    size="lg"
                    className="font-medium"
                    source="about-section"
                    analyticsEvent="about_schedule_consultation"
                    ctaText="Schedule Consultation"
                  />
                  <Button
                    as={Link}
                    href="/services"
                    variant="bordered"
                    color="primary"
                    size="lg"
                    className="font-medium"
                  >
                    View Our Services
                  </Button>
                </div>
              </div>
            </div>

            <div className="w-full px-4 lg:w-1/2">
              <div className="-mx-2 flex flex-wrap sm:-mx-4 lg:-mx-2 xl:-mx-4">
                <div className="w-full px-2 sm:w-1/2 sm:px-4 lg:px-2 xl:px-4">
                  <div
                    className="relative mb-4 overflow-hidden rounded-2xl shadow-lg sm:mb-8 sm:h-[400px] md:h-[540px] lg:h-[400px] xl:h-[500px]"
                  >
                    <Image
                      src="/images/about/FLUXDevImage4.webp"
                      alt="AI-powered software development at Digital Wave Systems"
                      fill
                      className="h-full w-full object-cover object-center transition-transform duration-500 hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <h3 className="text-xl font-semibold text-white">AI-Powered Solutions</h3>
                    </div>
                  </div>
                </div>

                <div className="w-full px-2 sm:w-1/2 sm:px-4 lg:px-2 xl:px-4">
                  <div className="relative mb-4 overflow-hidden rounded-2xl shadow-lg sm:mb-8 sm:h-[220px] md:h-[346px] lg:mb-4 lg:h-[225px] xl:mb-8 xl:h-[310px]">
                    <Image
                      src="/images/about/FLUXDevImage.jpeg"
                      alt="Cybersecurity solutions from Digital Wave Systems"
                      fill
                      className="h-full w-full object-cover object-center transition-transform duration-500 hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <h3 className="text-xl font-semibold text-white">Enterprise Security</h3>
                    </div>
                  </div>

                  <div className="relative z-10 mb-4 flex items-center justify-center overflow-hidden rounded-2xl bg-gradient-to-r from-primary to-blue-700 px-6 py-12 sm:mb-8 sm:h-[180px] sm:p-5 lg:mb-4 xl:mb-8 shadow-lg">
                    <div className="text-center">
                      <span className="mb-2 block text-5xl font-extrabold text-white">
                        6+
                      </span>
                      <span className="mb-1 block text-lg font-semibold text-white">
                        Years of Experience
                      </span>
                      <span className="block text-base font-medium text-white text-opacity-80">
                        In AI & Software Development
                      </span>
                    </div>
                    <div>
                      <span className="absolute left-0 top-0 -z-10">
                        <svg
                          width="106"
                          height="144"
                          viewBox="0 0 106 144"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            opacity="0.1"
                            x="-67"
                            y="47.127"
                            width="113.378"
                            height="131.304"
                            transform="rotate(-42.8643 -67 47.127)"
                            fill="url(#paint0_linear_1416_214)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_1416_214"
                              x1="-10.3111"
                              y1="47.127"
                              x2="-10.3111"
                              y2="178.431"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stopColor="white" />
                              <stop
                                offset="1"
                                stopColor="white"
                                stopOpacity="0"
                              />
                            </linearGradient>
                          </defs>
                        </svg>
                      </span>
                      <span className="absolute right-0 top-0 -z-10">
                        <svg
                          width="130"
                          height="97"
                          viewBox="0 0 130 97"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            opacity="0.1"
                            x="0.86792"
                            y="-6.67725"
                            width="155.563"
                            height="140.614"
                            transform="rotate(-42.8643 0.86792 -6.67725)"
                            fill="url(#paint0_linear_1416_215)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_1416_215"
                              x1="78.6495"
                              y1="-6.67725"
                              x2="78.6495"
                              y2="133.937"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stopColor="white" />
                              <stop
                                offset="1"
                                stopColor="white"
                                stopOpacity="0"
                              />
                            </linearGradient>
                          </defs>
                        </svg>
                      </span>
                      <span className="absolute bottom-0 right-0 -z-10">
                        <svg
                          width="175"
                          height="104"
                          viewBox="0 0 175 104"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            opacity="0.1"
                            x="175.011"
                            y="108.611"
                            width="101.246"
                            height="148.179"
                            transform="rotate(137.136 175.011 108.611)"
                            fill="url(#paint0_linear_1416_216)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_1416_216"
                              x1="225.634"
                              y1="108.611"
                              x2="225.634"
                              y2="256.79"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stopColor="white" />
                              <stop
                                offset="1"
                                stopColor="white"
                                stopOpacity="0"
                              />
                            </linearGradient>
                          </defs>
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Visual separator */}
          <Spacer y={8} />
          <Divider className="max-w-xs mx-auto" />
          <Spacer y={8} />

          {/* Key features section */}
          <div className="mt-8">
            <div className="text-center mb-12">
              <h3 className="text-xl font-bold text-primary mb-2">Why Choose Us</h3>
              <h2 className="text-3xl font-bold text-dark dark:text-white">Our Approach to AI Solutions</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  id: "expert-team",
                  title: "Expert AI Team",
                  description: "PhD-level AI researchers and experienced software engineers",
                  icon: <UserGroupIcon className="h-10 w-10 text-primary" />
                },
                {
                  id: "custom-solutions",
                  title: "Custom Solutions",
                  description: "Tailored AI applications built for your specific business needs",
                  icon: <PuzzlePieceIcon className="h-10 w-10 text-primary" />
                },
                {
                  id: "security-focus",
                  title: "Security Focus",
                  description: "Enterprise-grade security built into every solution we create",
                  icon: <ShieldCheckIcon className="h-10 w-10 text-primary" />
                },
                {
                  id: "continuous-support",
                  title: "Continuous Support",
                  description: "Ongoing maintenance and enhancement for lasting value",
                  icon: <BoltIcon className="h-10 w-10 text-primary" />
                }
              ].map((feature) => (
                <Card key={feature.id} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <CardBody className="p-8">
                    <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-lg bg-primary/10 dark:bg-primary/20">
                      {feature.icon}
                    </div>
                    <h3 className="mb-4 text-xl font-bold text-dark dark:text-white">{feature.title}</h3>
                    <p className="text-body-color dark:text-[#8890AD]">{feature.description}</p>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
