import PageHero from "@/components/Common/PageHero";
import NotFound from "@/components/NotFound";
import { Metadata } from "next";
import React from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import Link from "next/link";
import { GetQuoteServicesCTA } from "@/components/CTAForms/ProjectCTAButtons";

export const metadata: Metadata = {
  title: "404 Page | Digital Wave Systems LLC",
};

const ErrorPage = () => {
  return (
    <>
      <PageHero
        badge="Error 404"
        title="Page Not Found"
        description="The page you're looking for doesn't exist. It might have been moved or deleted."
        primaryCTA={
          <Button
            as={Link}
            href="/"
            variant="solid"
            size="lg"
            className="bg-white text-primary hover:bg-blue-50 font-semibold"
          >
            Go to Home
          </Button>
        }
        secondaryCTA={
          <GetQuoteServicesCTA
            variant="outline"
            size="lg"
            className="text-white border-white/30 hover:bg-white/10 font-medium"
            source="not-found"
            analyticsEvent="not_found_contact"
            ctaText="Contact Support"
          />
        }
      />

      <NotFound />
    </>
  );
};

export default ErrorPage;
