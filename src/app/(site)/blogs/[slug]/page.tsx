import Newsletter from "@/components/Blog/Newsletter";
import PopularArticle from "@/components/Blog/PopularArticle";
import PageHero from "@/components/Common/PageHero";
import { getAllPosts, getPostBySlug } from "@/utils/markdown";
import markdownToHtml from "@/utils/markdownToHtml";
import Image from "next/image";
import React from "react";
import { Metadata } from 'next';

type Props = {
  params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params; 
  const post = getPostBySlug(slug, [
    "title",
    "author",
    "content",
    "metadata",
  ]);

  const siteName = process.env.SITE_NAME ?? "Digital Wave Systems";

  if (post) {
    return {
      title: `${post.title || "Service Details"} | ${siteName}`,
      description: post.excerpt || "Digital Wave Systems professional services and solutions",
      openGraph: {
        images: ['/images/logo/Boxy18.svg'],
      },
      icons: {
        icon: '/images/logo/Boxy18.svg',
      },
    };
  } else {
    return {
      title: "Service Not Found",
      description: "The requested service information could not be found",
    };
  }
}

export default async function Post({ params }: Props) {
  const posts = getAllPosts(["title", "date", "excerpt", "coverImage", "slug"]);
  const { slug } = await params; 
  const post = getPostBySlug(slug, [
    "title",
    "author",
    "authorImage",
    "content",
    "coverImage",
    "date",
  ]);

  const content = await markdownToHtml(post.content || "");

  return (
    <>
      <PageHero
        badge="Blog Article"
        title={post.title || "Blog Post"}
        description="Read our latest insights and best practices in software development."
        primaryButtonText="View All Posts"
        primaryButtonLink="/blogs"
        secondaryButtonText="Contact Us"
        secondaryButtonLink="/contact"
      />

      <section className="pb-10 pt-20 bg-gradient-to-br from-background to-content1 lg:pb-20 lg:pt-[120px]">
        <div className="container">
          <div className="-mx-4 flex flex-wrap justify-center">
            <div className="w-full px-4">
              <div
                className="wow fadeInUp relative z-20 mb-[60px] h-[300px] overflow-hidden rounded-xl shadow-lg border border-divider md:h-[400px] lg:h-[500px]"
                data-wow-delay=".1s"
              >
                <Image
                  src={post.coverImage}
                  alt={post.title || "Blog post"}
                  width={1288}
                  height={500}
                  className="h-full w-full object-cover object-center transition-transform duration-300 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/80 to-transparent"></div>
                <div className="absolute bottom-0 left-0 w-full p-8">
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">{post.title}</h1>
                  <div className="inline-flex items-center rounded-full bg-primary/30 backdrop-blur-sm px-4 py-2 text-sm font-medium text-white border border-primary/20">
                    Digital Wave Systems Blog
                  </div>
                </div>
              </div>
              
              <div className="-mx-4 flex flex-wrap">
                <div className="w-full px-4 lg:w-8/12">
                  <div className="blog-details rounded-xl border border-divider bg-content1 shadow-lg p-8 xl:pr-10">
                    <div
                      className="blog-content max-w-none prose prose-lg prose-primary dark:prose-invert"
                      dangerouslySetInnerHTML={{ __html: content }}
                    ></div>
                  </div>
                </div>
                
                <div className="w-full px-4 lg:w-4/12">
                  <div className="sticky top-24 space-y-8">
                    <div className="wow fadeInUp rounded-xl border border-divider bg-content1 shadow-lg p-6" data-wow-delay=".1s">
                      <Newsletter />
                    </div>

                    <div className="wow fadeInUp rounded-xl border border-divider bg-content1 shadow-lg p-6" data-wow-delay=".2s">
                      <div className="-mx-4 mb-8 flex flex-wrap">
                        <div className="w-full px-4">
                          <h2 className="relative pb-5 text-2xl font-semibold text-foreground sm:text-[28px]">
                            Related Articles
                            <span className="mt-2 block h-[2px] w-20 bg-primary"></span>
                          </h2>
                        </div>
                        <div className="w-full px-4">
                          <div className="grid gap-4">
                            {posts.slice(0, 3).map((blog, i) => (
                              <PopularArticle
                                key={i}
                                coverImage={blog?.coverImage}
                                title={blog?.title.slice(0, 30)}
                                slug={blog?.slug}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="wow fadeInUp rounded-xl border border-divider bg-content1 shadow-lg p-6" data-wow-delay=".3s">
                      <div className="flex flex-col items-center text-center">
                        <Image
                          src="/images/logo/Boxy18.svg"
                          alt="Digital Wave Systems"
                          width={100}
                          height={100}
                          className="mb-4"
                        />
                        <h3 className="text-xl font-semibold text-foreground mb-2">Need Custom Solutions?</h3>
                        <p className="text-default-600 mb-6">Contact our team to discuss your specific requirements</p>
                        <a
                          href="/contact"
                          className="inline-flex items-center justify-center rounded-lg bg-primary px-6 py-3 text-center text-base font-medium text-primary-foreground hover:bg-primary/90 transition-all duration-200 transform hover:-translate-y-1 hover:shadow-lg"
                        >
                          Contact Us
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
