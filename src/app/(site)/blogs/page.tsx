import SingleBlog from "@/components/Blog/SingleBlog";
import PageHero from "@/components/Common/PageHero";
import { getAllPosts } from "@/utils/markdown";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Software Development Blog | Digital Wave Systems",
  description: "Stay updated with the latest insights, tutorials, and best practices in software development, AI integration, cybersecurity, and technology trends. Expert articles from our development team.",
  keywords: "software development blog, programming tutorials, AI integration guides, cybersecurity best practices, technology trends, software engineering, web development, mobile development, cloud computing, DevOps practices, machine learning, enterprise software",
  authors: [{ name: "Digital Wave Systems" }],
  creator: "Digital Wave Systems",
  publisher: "Digital Wave Systems",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "Software Development Blog | Digital Wave Systems",
    description: "Expert insights and tutorials on software development, AI integration, and technology trends from our professional development team.",
    type: "website",
    url: "https://digitalwavesystems.com.co/blogs",
    siteName: "Digital Wave Systems",
    images: [
      {
        url: "/images/logo/Logo.svg",
        width: 1200,
        height: 630,
        alt: "Digital Wave Systems - Software Development Blog",
      },
    ],
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Software Development Blog | Digital Wave Systems",
    description: "Expert insights and tutorials on software development and technology trends.",
    images: ["/images/logo/Logo.svg"],
    creator: "@digitalwavesys",
    site: "@digitalwavesys",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com.co/blogs",
  },
  category: "Technology",
};

const Blog = () => {
  const posts = getAllPosts(["title", "date", "excerpt", "coverImage", "slug"]);

  return (
    <>
      <PageHero
        badge="Our Blog"
        title="Software Development Insights & Best Practices"
        description="Stay updated with the latest insights, tutorials, and best practices in software development, AI integration, cybersecurity, and technology trends from our expert team."
        primaryButtonText="Subscribe to Updates"
        primaryButtonLink="/contact"
        secondaryButtonText="View Our Services"
        secondaryButtonLink="/services"
      />

      <section className="pb-10 pt-20 lg:pb-20 lg:pt-[120px]">
        <div className="container">
          <div className="-mx-4 flex flex-wrap justify-center">
            {posts.map((blog, i) => (
              <div key={blog.slug || i} className="w-full px-4 md:w-2/3 lg:w-1/2 xl:w-1/3">
                <SingleBlog blog={blog} />
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default Blog;
