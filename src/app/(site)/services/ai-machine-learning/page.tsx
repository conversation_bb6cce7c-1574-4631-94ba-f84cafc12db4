import React from 'react';
import { Metadata } from 'next';
import ServiceDetailPage from '@/components/Services/ServiceDetailPage';

export const metadata: Metadata = {
  title: "AI & Machine Learning Integration Services | Digital Wave Systems",
  description: "Transform your business with intelligent automation, predictive analytics, and machine learning solutions that drive real results. Expert AI/ML development and integration services.",
  keywords: "AI integration, machine learning, artificial intelligence, predictive analytics, automation, data science, neural networks, deep learning, AI consulting",
  openGraph: {
    title: "AI & Machine Learning Integration Services | Digital Wave Systems",
    description: "Expert AI/ML integration services to automate processes and gain predictive insights.",
    type: "website",
    url: "https://digitalwavesystems.com/services/ai-machine-learning",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI & Machine Learning Integration Services | Digital Wave Systems",
    description: "Expert AI/ML integration services to automate processes and gain predictive insights.",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com/services/ai-machine-learning",
  },
};

// Get the AI/ML service data from the main services data
const aiMlService = servicesData.find(service => service.id === "ai-ml")!;

// Enhanced service data with additional professional content
const serviceData = {
  ...aiMlService,
  faq: [
    {
      question: "What types of AI/ML solutions do you develop?",
      answer: "We develop a wide range of AI/ML solutions including predictive analytics, natural language processing, computer vision, recommendation systems, chatbots, and automated decision-making systems tailored to your specific business needs."
    },
    {
      question: "How long does it take to implement AI/ML solutions?",
      answer: "Implementation timelines vary based on complexity, but typical projects range from 2-8 months. We start with proof-of-concept development, followed by full implementation and integration with your existing systems."
    },
    {
      question: "Do you provide training for our team?",
      answer: "Yes, we provide comprehensive training for your team on using and maintaining AI/ML systems. We also offer ongoing support and can help you build internal AI capabilities over time."
    },
    {
      question: "What data do you need for AI/ML projects?",
      answer: "Data requirements vary by project type. We work with you to assess your existing data, identify gaps, and develop data collection strategies. We can work with structured and unstructured data from various sources."
    },
    {
      question: "How do you ensure AI model accuracy and reliability?",
      answer: "We use rigorous testing methodologies, cross-validation techniques, and continuous monitoring to ensure model accuracy. We also implement bias detection and fairness measures to ensure reliable and ethical AI solutions."
    }
  ]
};

export default function AIMachineLearningPage() {
  return <SimpleServiceDetailPage service={serviceData} />;
}
