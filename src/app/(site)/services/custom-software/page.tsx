import React from 'react';
import { Metadata } from 'next';
import ServiceDetailPage from '@/components/Services/ServiceDetailPage';
import { servicesData } from '../../../../data/services';

export const metadata: Metadata = {
  title: "Custom Software Development Services | Digital Wave Systems",
  description: "Transform your business with bespoke software solutions. End-to-end custom software development from web applications to enterprise systems. Expert team, proven methodologies, guaranteed results.",
  keywords: "custom software development, bespoke software, enterprise applications, web development, mobile development, software consulting, full-stack development, software architecture",
  openGraph: {
    title: "Custom Software Development Services | Digital Wave Systems",
    description: "Expert custom software development services to accelerate your digital transformation.",
    type: "website",
    url: "https://digitalwavesystems.com/services/custom-software",
  },
  twitter: {
    card: "summary_large_image",
    title: "Custom Software Development Services | Digital Wave Systems",
    description: "Expert custom software development services to accelerate your digital transformation.",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com/services/custom-software",
  },
};

// Get the custom software service data from the main services data
const customSoftwareService = servicesData.find(service => service.id === "custom-software")!;

// Enhanced service data with additional professional content
const serviceData = {
  ...customSoftwareService,
  faq: [
    {
      question: "How long does custom software development typically take?",
      answer: "Development timelines vary based on project complexity, but most projects range from 3-12 months. We provide detailed timelines during the planning phase and use agile methodologies to deliver working software incrementally."
    },
    {
      question: "Do you provide ongoing support and maintenance?",
      answer: "Yes, we offer comprehensive support and maintenance packages including bug fixes, security updates, performance monitoring, and feature enhancements to ensure your software continues to meet your evolving needs."
    },
    {
      question: "Can you integrate with our existing systems?",
      answer: "Absolutely. We specialize in system integration and can connect your new software with existing databases, CRM systems, ERP platforms, and third-party services through robust APIs and integration layers."
    },
    {
      question: "What technologies do you use for development?",
      answer: "We use modern, proven technologies based on your specific needs. Our tech stack includes React, Node.js, Python, Java, cloud platforms (AWS, Azure), mobile frameworks (React Native, Flutter), and various databases."
    },
    {
      question: "How do you ensure software quality and security?",
      answer: "We follow industry best practices including automated testing, code reviews, security audits, and compliance with security standards. Our development process includes continuous integration and deployment with comprehensive quality assurance."
    }
  ]
};

export default function CustomSoftwarePage() {
  return  <ServiceDetailPage service={serviceData} />;
}
