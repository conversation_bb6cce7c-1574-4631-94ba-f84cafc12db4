import React from 'react';
import { Metadata } from 'next';
import ServiceDetailPage from '@/components/Services/ServiceDetailPage';

export const metadata: Metadata = {
  title: "Custom Software Development Services | Digital Wave Systems",
  description: "Transform your business with bespoke software solutions. End-to-end custom software development from web applications to enterprise systems. Expert team, proven methodologies, guaranteed results.",
  keywords: "custom software development, bespoke software, enterprise applications, web development, mobile development, software consulting, full-stack development, software architecture",
  openGraph: {
    title: "Custom Software Development Services | Digital Wave Systems",
    description: "Expert custom software development services to accelerate your digital transformation.",
    type: "website",
    url: "https://digitalwavesystems.com/services/custom-software",
  },
  twitter: {
    card: "summary_large_image",
    title: "Custom Software Development Services | Digital Wave Systems",
    description: "Expert custom software development services to accelerate your digital transformation.",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com/services/custom-software",
  },
};

const serviceData = {
  id: "custom-software",
  title: "Custom Software Development",
  subtitle: "Transform Your Business with Bespoke Solutions",
  heroImage: "/images/services/custom-software-hero.jpg",
  description: "Transform your business with bespoke software solutions designed specifically for your unique requirements. Our expert development team creates scalable, secure, and user-friendly applications that drive efficiency and growth.",
  
  overview: {
    title: "End-to-End Custom Software Solutions",
    content: "We specialize in creating tailored software solutions that perfectly align with your business objectives. From initial concept to deployment and ongoing support, our comprehensive approach ensures your software investment delivers maximum value and competitive advantage.",
    benefits: [
      "Increased operational efficiency and productivity",
      "Reduced manual processes and human error", 
      "Scalable solutions that grow with your business",
      "Competitive advantage through custom features",
      "Better data insights and reporting capabilities",
      "Improved customer experience and satisfaction"
    ]
  },

  services: [
    {
      title: "Full-Stack Web Development",
      description: "Modern, responsive web applications built with cutting-edge technologies and frameworks.",
      features: [
        "React, Angular, Vue.js frontend development",
        "Node.js, Python, Java backend systems",
        "RESTful and GraphQL API development",
        "Progressive Web App (PWA) implementation",
        "Real-time features with WebSocket integration",
        "Responsive design for all devices"
      ]
    },
    {
      title: "Mobile App Development", 
      description: "Native and cross-platform mobile applications for iOS and Android platforms.",
      features: [
        "Native iOS development with Swift",
        "Native Android development with Kotlin",
        "Cross-platform development with React Native",
        "Flutter development for high-performance apps",
        "Mobile app UI/UX design and optimization",
        "App store deployment and maintenance"
      ]
    },
    {
      title: "Enterprise Software Solutions",
      description: "Robust enterprise-grade applications designed for scalability and security.",
      features: [
        "Enterprise Resource Planning (ERP) systems",
        "Customer Relationship Management (CRM) platforms",
        "Business Intelligence and analytics dashboards",
        "Workflow automation and process optimization",
        "Integration with existing enterprise systems",
        "Advanced security and compliance features"
      ]
    },
    {
      title: "API Development & Integration",
      description: "Seamless system integration and API development for connected ecosystems.",
      features: [
        "RESTful API design and development",
        "GraphQL API implementation",
        "Third-party service integrations",
        "Microservices architecture design",
        "API documentation and testing",
        "Legacy system modernization"
      ]
    }
  ],

  process: {
    title: "Our Development Process",
    steps: [
      {
        phase: "Discovery & Planning",
        duration: "1-2 weeks",
        description: "Requirements gathering, technical analysis, and project roadmap creation.",
        deliverables: ["Project requirements document", "Technical architecture plan", "Development timeline"]
      },
      {
        phase: "Design & Prototyping", 
        duration: "2-3 weeks",
        description: "UI/UX design, system architecture, and interactive prototypes.",
        deliverables: ["UI/UX designs", "System architecture diagrams", "Interactive prototypes"]
      },
      {
        phase: "Development & Testing",
        duration: "8-16 weeks",
        description: "Agile development with continuous testing and quality assurance.",
        deliverables: ["Working software increments", "Test reports", "Code documentation"]
      },
      {
        phase: "Deployment & Launch",
        duration: "1-2 weeks", 
        description: "Production deployment, performance optimization, and go-live support.",
        deliverables: ["Production deployment", "Performance reports", "User training materials"]
      },
      {
        phase: "Support & Maintenance",
        duration: "Ongoing",
        description: "Continuous monitoring, updates, and feature enhancements.",
        deliverables: ["Maintenance reports", "Feature updates", "Performance monitoring"]
      }
    ]
  },

  technologies: [
    "React", "Node.js", "Python", "Java", "React Native", "Flutter", 
    "PostgreSQL", "MongoDB", "AWS", "Docker", "Kubernetes", "TypeScript"
  ],

  pricing: {
    title: "Flexible Pricing Models",
    description: "Choose the pricing model that best fits your project needs and budget.",
    models: [
      {
        name: "Fixed Price",
        description: "Best for well-defined projects with clear requirements",
        price: "Starting from $25,000",
        features: [
          "Detailed project scope and timeline",
          "Fixed cost with no surprises",
          "Milestone-based payments",
          "Complete project delivery"
        ],
        recommended: false
      },
      {
        name: "Time & Materials",
        description: "Ideal for projects with evolving requirements",
        price: "$100-200/hour",
        features: [
          "Flexible scope adjustments",
          "Transparent hourly billing",
          "Regular progress reports",
          "Agile development approach"
        ],
        recommended: true
      },
      {
        name: "Dedicated Team",
        description: "For long-term projects requiring dedicated resources",
        price: "Starting from $15,000/month",
        features: [
          "Dedicated development team",
          "Full-time resource allocation",
          "Direct team communication",
          "Scalable team size"
        ],
        recommended: false
      }
    ]
  },

  caseStudy: {
    title: "E-commerce Platform Transformation",
    client: "Leading Retail Company",
    challenge: "Legacy e-commerce platform couldn't handle growing traffic and needed modern features for better customer experience.",
    solution: "Built a scalable, modern e-commerce platform with advanced features including real-time inventory, personalized recommendations, and mobile-first design.",
    results: [
      {
        metric: "300% increase",
        description: "in online sales within 6 months"
      },
      {
        metric: "50% reduction", 
        description: "in cart abandonment rate"
      },
      {
        metric: "99.9% uptime",
        description: "with scalable cloud infrastructure"
      },
      {
        metric: "40% faster",
        description: "page load times"
      }
    ],
    testimonial: {
      quote: "Digital Wave Systems transformed our online presence completely. The new platform not only handles our growing traffic but has significantly improved our conversion rates and customer satisfaction.",
      author: "Sarah Johnson",
      title: "CTO",
      company: "RetailMax Solutions"
    }
  },

  faq: [
    {
      question: "How long does custom software development typically take?",
      answer: "Development timelines vary based on project complexity, but most projects range from 3-12 months. We provide detailed timelines during the planning phase and use agile methodologies to deliver working software incrementally."
    },
    {
      question: "Do you provide ongoing support and maintenance?",
      answer: "Yes, we offer comprehensive support and maintenance packages including bug fixes, security updates, performance monitoring, and feature enhancements to ensure your software continues to meet your evolving needs."
    },
    {
      question: "Can you integrate with our existing systems?",
      answer: "Absolutely. We specialize in system integration and can connect your new software with existing databases, CRM systems, ERP platforms, and third-party services through robust APIs and integration layers."
    },
    {
      question: "What technologies do you use for development?",
      answer: "We use modern, proven technologies based on your specific needs. Our tech stack includes React, Node.js, Python, Java, cloud platforms (AWS, Azure), mobile frameworks (React Native, Flutter), and various databases."
    },
    {
      question: "How do you ensure software quality and security?",
      answer: "We follow industry best practices including automated testing, code reviews, security audits, and compliance with security standards. Our development process includes continuous integration and deployment with comprehensive quality assurance."
    }
  ]
};

export default function CustomSoftwarePage() {
  return <ServiceDetailPage service={serviceData} />;
}
