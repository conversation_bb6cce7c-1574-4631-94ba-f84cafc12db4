import PageHero from "@/components/Common/PageHero";
import Faq from "@/components/Faq";
import Pricing from "@/components/Pricing";
import { Metadata } from "next";
import { But<PERSON> } from "@heroui/react";
import Link from "next/link";
import { GetQuoteServicesCTA } from "@/components/CTAForms/ProjectCTAButtons";

export const metadata: Metadata = {
  title: "Software Development Pricing | Digital Wave Systems",
  description: "Transparent pricing for custom software development, enterprise solutions, and technology consulting services. Choose the plan that fits your business needs.",
};

const PricingPage = () => {
  return (
    <>
      <PageHero
        badge="Pricing Plans"
        title="Transparent Software Development Pricing"
        description="Choose the perfect plan for your software development needs. From startups to enterprise, we have flexible pricing options that scale with your business."
        primaryCTA={
          <GetQuoteServicesCTA
            variant="primary"
            size="lg"
            className="bg-white text-primary hover:bg-blue-50 font-semibold"
            source="pricing-hero"
            analyticsEvent="pricing_hero_get_quote"
          />
        }
        secondaryCTA={
          <Button
            as={Link}
            href="/services"
            variant="bordered"
            size="lg"
            className="text-white border-white/30 hover:bg-white/10 font-medium"
          >
            View Services
          </Button>
        }
      />
      <Pricing />
      <Faq />
    </>
  );
};

export default PricingPage;
