import About from "@/components/About";
import PageHero from "@/components/Common/PageHero";
import Team from "@/components/Team";
import { Metadata } from "next";
import { <PERSON><PERSON> } from "@heroui/react";
import Link from "next/link";
import { StartProjectCTA } from "@/components/CTAForms/ProjectCTAButtons";

export const metadata: Metadata = {
  title: "About Digital Wave Systems | Leading Software Development Company",
  description: "Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, full-stack development, and technology consulting services.",
  keywords: "Digital Wave Systems, software development company, custom software solutions, enterprise software development, full-stack development, software consulting, technology services",
  openGraph: {
    title: "About Digital Wave Systems | Leading Software Development Company",
    description: "Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, and technology consulting services.",
    images: ['/images/about/FLUXDevImage4.webp'],
    type: 'website',
  },
};

const AboutPage = () => {
  return (
    <main>
      <PageHero
        badge="About Digital Wave Systems"
        title="Your Trusted Software Consulting Partner"
        description="Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, and technology consulting services."
        primaryCTA={
          <StartProjectCTA
            variant="primary"
            size="lg"
            className="bg-white text-primary hover:bg-blue-50 font-semibold"
            source="about-hero"
            analyticsEvent="about_hero_start_project"
          />
        }
        secondaryCTA={
          <Button
            as={Link}
            href="/services"
            variant="bordered"
            size="lg"
            className="text-white border-white/30 hover:bg-white/10 font-medium"
          >
            View Our Services
          </Button>
        }
      />
      <About />
      <Team />
    </main>
  );
};

export default AboutPage;
