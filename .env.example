# Database
DATABASE_URL="your_database_url_here"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret_here"

# Stripe
STRIPE_PUBLISHABLE_KEY="your_stripe_publishable_key_here"
STRIPE_SECRET_KEY="your_stripe_secret_key_here"

# Email (Nodemailer)
EMAIL_HOST="your_email_host_here"
EMAIL_PORT=587
EMAIL_USER="your_email_user_here"
EMAIL_PASS="your_email_password_here"

# n8n Webhook Integration
# Required: Secret key for webhook authentication
N8N_WEBHOOK_SECRET="your_n8n_webhook_secret_here"

# Optional: Individual webhook URLs for different automation workflows
# Lead Capture Workflow - Handles general lead capture and qualification
N8N_LEAD_CAPTURE_WEBHOOK_URL="https://n8n-4u4r.onrender.com/webhook/lead-capture"

# Consultation Booking Workflow - Handles consultation scheduling and calendar integration
N8N_CONSULTATION_WEBHOOK_URL="https://your-n8n-instance.com/webhook/consultation-booking"

# Quick Quote Workflow - Handles simplified quote requests with immediate consultation options
N8N_QUICK_QUOTE_WEBHOOK_URL="https://your-n8n-instance.com/webhook/quick-quote"

# Start Project Workflow - Handles high-intent project inquiries with detailed requirements
N8N_START_PROJECT_WEBHOOK_URL="https://your-n8n-instance.com/webhook/start-project"

# Get Quote Workflow - Handles lead generation quote requests for nurturing
N8N_GET_QUOTE_WEBHOOK_URL="https://your-n8n-instance.com/webhook/get-quote"

# Phone Callback Workflow - Handles immediate phone callback requests
N8N_PHONE_CALLBACK_WEBHOOK_URL="https://your-n8n-instance.com/webhook/phone-callback"

# Video Consultation Workflow - Handles video consultation lead capture
N8N_VIDEO_CONSULTATION_WEBHOOK_URL="https://your-n8n-instance.com/webhook/video-consultation"

# Project Estimation Workflow - Handles project estimation requests and proposal generation
N8N_ESTIMATION_WEBHOOK_URL="https://your-n8n-instance.com/webhook/project-estimation"

# Payment Workflow - Handles payment completion events and follow-up automation
N8N_PAYMENT_WEBHOOK_URL="https://your-n8n-instance.com/webhook/payment-completed"

# Support Workflow - Handles support requests and ticket creation
N8N_SUPPORT_WEBHOOK_URL="https://your-n8n-instance.com/webhook/support-request"

# Optional: Additional n8n configuration
N8N_INSTANCE_URL="https://your-n8n-instance.com"
N8N_API_KEY="your_n8n_api_key_here"

# Optional: CRM Integration (if using n8n to sync with CRM)
CRM_API_URL="your_crm_api_url_here"
CRM_API_KEY="your_crm_api_key_here"

# Optional: Calendar Integration (for consultation booking)
CALENDAR_PROVIDER="google" # or "outlook", "calendly"
GOOGLE_CALENDAR_CLIENT_ID="your_google_calendar_client_id"
GOOGLE_CALENDAR_CLIENT_SECRET="your_google_calendar_client_secret"
GOOGLE_CALENDAR_REFRESH_TOKEN="your_google_calendar_refresh_token"

# Optional: Slack Integration (for internal notifications)
SLACK_WEBHOOK_URL="your_slack_webhook_url_here"
SLACK_CHANNEL="#sales-leads"

# Optional: Analytics and Tracking
GOOGLE_ANALYTICS_ID="your_google_analytics_id"
FACEBOOK_PIXEL_ID="your_facebook_pixel_id"
LINKEDIN_INSIGHT_TAG="your_linkedin_insight_tag"

# Optional: Lead Scoring and Enrichment
CLEARBIT_API_KEY="your_clearbit_api_key_here"
HUNTER_API_KEY="your_hunter_api_key_here"

# Optional: SMS Integration (for urgent notifications)
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"
TWILIO_PHONE_NUMBER="your_twilio_phone_number"

# Optional: Video Conferencing Integration
ZOOM_API_KEY="your_zoom_api_key"
ZOOM_API_SECRET="your_zoom_api_secret"
ZOOM_JWT_TOKEN="your_zoom_jwt_token"

# Development/Testing
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Optional: Rate Limiting and Security
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_MAX_RETRIES=3
